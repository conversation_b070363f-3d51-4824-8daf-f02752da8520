# DiaWise Project README

## Project Overview

Diawise is a platform focused on diabetes education and community support. We provide the following key features:

- **Educational Content:** Provides essential resources and expert insights to help users better understand and manage the condition.
- **Community:** Offers a space for individuals to connect with others on similar diabetes journeys, enabling them to share experiences and support each other in a safe environment.
- **Healthy Living Tips:** Delivers practical advice on diet, exercise, and lifestyle choices tailored to improve the well-being of people managing diabetes.
- **Diet Management:** Track meals and nutritional intake tailored to diabetes management.

The platform encourages users to join a vibrant, supportive community dedicated to empowering individuals in their diabetes management journey.

## Features


## Tech Stack
- **Backend:** Go (Golang)
- **Frontend:** JavaScript
- **Database:** SQLite

## Running the Application

To run the DiaWise application, follow these steps:

1. Clone the repository to your local machine.
```
https://github.com/Siaka385/diabetics_management_app.git
```
2. Navigate to the backend directory:
   ```bash
   cd diabeticd_managemrnt_app
   ```
 3. Run the application using Go:
```bash
go run .
```
## Contributors
1. <PERSON>
2. <PERSON>
3. <PERSON>
4. <PERSON>
5. <PERSON>



`**Note:** This project is still in progress. Features and functionalities are being developed and improved.`

