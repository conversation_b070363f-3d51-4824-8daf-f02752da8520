package services

import (
	"encoding/json"
	"fmt"
	"strconv"
	"strings"

	"gorm.io/gorm"
)

type MealType string

func (profile *DietProfile) ParseDietProfileString(data string) error {
	// Check if the input string appears to be in JSON format
	if strings.HasPrefix(strings.TrimSpace(data), "{") {
		err := json.Unmarshal([]byte(data), profile)
		if err == nil {
			return nil
		}
	}
	lines := strings.Split(data, "\n")

	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" {
			continue
		}
		line = strings.ReplaceAll(line, ",", "")
		line = strings.ReplaceAll(line, "\"", "")
		line = strings.ReplaceAll(line, ":", "")
		parts := strings.Fields(line)
		if len(parts) != 2 {
			continue
		}

		key := parts[0]
		value, err := strconv.ParseFloat(parts[1], 64)
		if err != nil {
			return fmt.Errorf("error parsing value for %s: %w", key, err)
		}

		switch key {
		case "CaloriesIntake":
			profile.CaloriesIntake = value
		case "CarbIntake":
			profile.CarbIntake = value
		case "ProteinIntake":
			profile.ProteinIntake = value
		case "FatIntake":
			profile.FatIntake = value
		case "SugarConsumption":
			profile.SugarConsumption = value
		case "ProcessedFoodRatio":
			profile.ProcessedFoodRatio = value
		}

	}
	return nil
}

func SaveMealLog(db *gorm.DB, mealLog MealLogEntry) error {
	tx := db.Begin()
	if err := tx.Create(&mealLog).Error; err != nil {
		tx.Rollback()
		return err
	}
	tx.Commit()
	return nil
}

func SaveDietLog(db *gorm.DB, d DietProfile) error {
	tx := db.Begin()
	if err := tx.Create(&d).Error; err != nil {
		tx.Rollback()
		return err
	}
	tx.Commit()
	return nil
}
