package authentication

import (
	repository "diawise/internal/repository/userRegistrationLoginRepository"
)

type Registration_service struct {
	RegistrationStore *repository.Registration
}

func NewRegistration(store *repository.Registration) *Registration_service {
	return &Registration_service{RegistrationStore: store}
}

type Login_service struct {
	LoginStore *repository.Login
}

func NewLogin(store *repository.Login) *Login_service {
	return &Login_service{LoginStore: store}
}
